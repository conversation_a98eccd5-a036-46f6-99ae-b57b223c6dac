import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';

class ChangePasswordMobile extends ConsumerStatefulWidget {
  const ChangePasswordMobile({super.key});

  @override
  ConsumerState<ChangePasswordMobile> createState() =>
      _ChangePasswordMobileState();
}

class _ChangePasswordMobileState extends ConsumerState<ChangePasswordMobile> {
  // Password controllers
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmNewPasswordController =
      TextEditingController();

  // State for checkbox (newsletter)
  bool _subscribeNewsletter = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmNewPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSavePassword() async {
    // Basic validation
    if (_currentPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter your current password', isError: true);
      return;
    }
    if (_newPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter a new password', isError: true);
      return;
    }
    if (_confirmNewPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please confirm your new password', isError: true);
      return;
    }
    if (_newPasswordController.text.trim() !=
        _confirmNewPasswordController.text.trim()) {
      Utils().showFlushbar(context,
          message: 'Passwords do not match', isError: true);
      return;
    }

    if (!mounted) return;

    // Call reset password API
    await ref.read(userProfileNotifierProvider.notifier).resetPassword(
      context,
      _currentPasswordController.text.trim(),
      _newPasswordController.text.trim(),
      _confirmNewPasswordController.text.trim(),
      onSuccess: () {
        // Clear fields on success
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmNewPasswordController.clear();
      },
    );
  }

  Future<void> _handleRemoveAccount() async {
    final bool? shouldDelete = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Remove Account',
      subtitle:
          'Are you sure you want to remove your account? This action cannot be undone.',
      confirmText: 'Remove',
      cancelText: 'Cancel',
    );

    if (shouldDelete == true && context.mounted) {
      await ref
          .read(userProfileNotifierProvider.notifier)
          .deleteAccount(context);
    }
  }

  Widget _buildPasswordField(String label, TextEditingController controller,
      {bool showDots = false}) {
    return Row(
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            height: 45,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: TextFormField(
              controller: controller,
              obscureText: true,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: showDots ? '••••••••' : '',
                hintStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                border: InputBorder.none,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final isLoading = userProfileState.status == AppStatus.loading;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.red,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Change Password Section
            const Text(
              'Change Password:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 20),

            // Current Password Field
            _buildPasswordField('Current Password', _currentPasswordController,
                showDots: true),
            const SizedBox(height: 16),

            // New Password Field
            _buildPasswordField('New Password', _newPasswordController),
            const SizedBox(height: 16),

            // Confirm New Password Field
            _buildPasswordField(
                'Confirm New Password', _confirmNewPasswordController),
            const SizedBox(height: 24),

            // Save Password Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleSavePassword,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Save Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 32),

            // Newsletter Section
            const Text(
              'Newsletter:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),

            // Subscribe Newsletter Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Subscribe Newsletter',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.grey,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _subscribeNewsletter = !_subscribeNewsletter;
                    });
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: _subscribeNewsletter ? Colors.red : Colors.white,
                      border: Border.all(
                        color: _subscribeNewsletter
                            ? Colors.red
                            : Colors.grey[400]!,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _subscribeNewsletter
                        ? const Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
              ],
            ),

            // Spacer to push Remove Account to bottom
            const SizedBox(height: 200),

            // Remove Account Button
            Center(
              child: GestureDetector(
                onTap: _handleRemoveAccount,
                child: const Text(
                  'Remove Account',
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
