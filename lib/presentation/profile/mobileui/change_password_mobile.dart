import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';

class ChangePasswordMobile extends ConsumerStatefulWidget {
  const ChangePasswordMobile({super.key});

  @override
  ConsumerState<ChangePasswordMobile> createState() =>
      _ChangePasswordMobileState();
}

class _ChangePasswordMobileState extends ConsumerState<ChangePasswordMobile> {
  // Password controllers
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmNewPasswordController =
      TextEditingController();

  // State for checkbox (newsletter)
  bool _subscribeNewsletter = true;

  // Password visibility states
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmNewPasswordController.dispose();
    super.dispose();
  }

  Future<void> _handleSavePassword() async {
    // Basic validation
    if (_currentPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter your current password', isError: true);
      return;
    }
    if (_newPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please enter a new password', isError: true);
      return;
    }
    if (_confirmNewPasswordController.text.trim().isEmpty) {
      Utils().showFlushbar(context,
          message: 'Please confirm your new password', isError: true);
      return;
    }
    if (_newPasswordController.text.trim() !=
        _confirmNewPasswordController.text.trim()) {
      Utils().showFlushbar(context,
          message: 'Passwords do not match', isError: true);
      return;
    }

    if (!mounted) return;

    // Call reset password API
    await ref.read(userProfileNotifierProvider.notifier).resetPassword(
      context,
      _currentPasswordController.text.trim(),
      _newPasswordController.text.trim(),
      _confirmNewPasswordController.text.trim(),
      onSuccess: () {
        // Clear fields on success
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmNewPasswordController.clear();
      },
    );
  }

  Future<void> _handleRemoveAccount() async {
    final bool? shouldDelete = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Remove Account',
      subtitle:
          'Are you sure you want to remove your account? This action cannot be undone.',
      confirmText: 'Remove',
      cancelText: 'Cancel',
    );

    if (shouldDelete == true && context.mounted) {
      await ref
          .read(userProfileNotifierProvider.notifier)
          .deleteAccount(context);
    }
  }

  Widget _buildPasswordField(String label, TextEditingController controller,
      {bool showDots = false,
      bool? isVisible,
      VoidCallback? onToggleVisibility}) {
    return Row(
      children: [
        SizedBox(
          width: 140,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            height: 30,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(5),
              border: Border.all(color: Colors.grey[300]!, width: 1),
            ),
            child: TextFormField(
              controller: controller,
              obscureText: isVisible != null ? !isVisible : true,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black87,
              ),
              decoration: InputDecoration(
                hintText: showDots ? '••••••••' : '',
                hintStyle: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                border: InputBorder.none,
                suffixIcon: onToggleVisibility != null
                    ? GestureDetector(
                        onTap: onToggleVisibility,
                        child: Icon(
                          isVisible == true
                              ? Icons.visibility
                              : Icons.visibility_off,
                          color: Colors.grey[600],
                          size: 18,
                        ),
                      )
                    : null,
              ),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final isLoading = userProfileState.status == AppStatus.loading;

    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.red,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Change Password Section
            const Text(
              'Change Password:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 20),

            // Current Password Field
            _buildPasswordField(
              'Current Password',
              _currentPasswordController,
              showDots: true,
              isVisible: _isCurrentPasswordVisible,
              onToggleVisibility: () {
                setState(() {
                  _isCurrentPasswordVisible = !_isCurrentPasswordVisible;
                });
              },
            ),
            const SizedBox(height: 16),

            // New Password Field
            _buildPasswordField(
              'New Password',
              _newPasswordController,
              isVisible: _isNewPasswordVisible,
              onToggleVisibility: () {
                setState(() {
                  _isNewPasswordVisible = !_isNewPasswordVisible;
                });
              },
            ),
            const SizedBox(height: 16),

            // Confirm New Password Field
            _buildPasswordField(
              'Confirm New Password',
              _confirmNewPasswordController,
              isVisible: _isConfirmPasswordVisible,
              onToggleVisibility: () {
                setState(() {
                  _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                });
              },
            ),
            const SizedBox(height: 24),

            // Save Password Button
            SizedBox(
              width: double.infinity,
              height: 40,
              child: ElevatedButton(
                onPressed: isLoading ? null : _handleSavePassword,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  padding: const EdgeInsets.symmetric(vertical: 0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                    : const Text(
                        'Save Password',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
              ),
            ),
            const SizedBox(height: 32),

            // Newsletter Section
            const Text(
              'Newsletter:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 16),

            // Subscribe Newsletter Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'Subscribe Newsletter',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Colors.black,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _subscribeNewsletter = !_subscribeNewsletter;
                    });
                  },
                  child: Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(
                      color: _subscribeNewsletter
                          ? AppColors.primaryColor
                          : Colors.white,
                      border: Border.all(
                        color: _subscribeNewsletter
                            ? Colors.red
                            : Colors.grey[400]!,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: _subscribeNewsletter
                        ? const Icon(
                            Icons.check,
                            size: 14,
                            color: Colors.white,
                          )
                        : null,
                  ),
                ),
              ],
            ),

            // Spacer to push Remove Account to bottom
            const SizedBox(height: 200),

            // Remove Account Button
            Center(
              child: GestureDetector(
                onTap: _handleRemoveAccount,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withValues(alpha: 0.1),
                        spreadRadius: 1,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Text(
                    'Remove Account',
                    style: TextStyle(
                      color: AppColors.primaryColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
