import 'package:mastercookai/app/imports/core_imports.dart';
import 'package:mastercookai/app/imports/packages_imports.dart';
import 'package:mastercookai/core/network/app_status.dart';
import 'package:mastercookai/core/providers/profile/user_profile_notifier.dart';
import 'package:mastercookai/core/utils/Utils.dart';
import 'package:mastercookai/core/utils/validator.dart';
import 'package:mastercookai/core/widgets/custom_appbar.dart';
import 'package:mastercookai/core/widgets/custom_button.dart';
import 'package:mastercookai/core/widgets/custom_input_field.dart';
import 'package:mastercookai/core/widgets/custom_text.dart';

class ChangePasswordMobile extends ConsumerStatefulWidget {
  const ChangePasswordMobile({super.key});

  @override
  ConsumerState<ChangePasswordMobile> createState() =>
      _ChangePasswordMobileState();
}

class _ChangePasswordMobileState extends ConsumerState<ChangePasswordMobile> {
  // Form key for validation
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // Text controllers
  final TextEditingController _currentPasswordController =
      TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();

  // Password visibility states
  bool _isCurrentPasswordVisible = false;
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  // Newsletter subscription state
  final bool _subscribeNewsletter = true;

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Validate password match
  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your new password';
    }
    if (value != _newPasswordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  // Handle save password
  Future<void> _handleSavePassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!mounted) return;
    final currentContext = context;

    // Call reset password API
    await ref.read(userProfileNotifierProvider.notifier).resetPassword(
      currentContext,
      _currentPasswordController.text.trim(),
      _newPasswordController.text.trim(),
      _confirmPasswordController.text.trim(),
      onSuccess: () {
        // Clear fields on success
        _currentPasswordController.clear();
        _newPasswordController.clear();
        _confirmPasswordController.clear();
      },
    );

    // Check the final state after API call
    final userProfileState = ref.read(userProfileNotifierProvider);
    if (userProfileState.status == AppStatus.success && mounted) {
      // Clear fields again to ensure UI consistency
      _currentPasswordController.clear();
      _newPasswordController.clear();
      _confirmPasswordController.clear();
    }
  }

  // Handle remove account
  Future<void> _handleRemoveAccount() async {
    if (!mounted) return;

    final bool? shouldDelete = await Utils().showCommonConfirmDialog(
      context: context,
      title: 'Remove Account',
      subtitle:
          'Are you sure you want to remove your account? This action cannot be undone.',
      confirmText: 'Remove',
      cancelText: 'Cancel',
    );

    if (shouldDelete == true && context.mounted) {
      // Call delete account API - error handling is done in the provider
      await ref
          .read(userProfileNotifierProvider.notifier)
          .deleteAccount(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final userProfileState = ref.watch(userProfileNotifierProvider);
    final isLoading = userProfileState.status == AppStatus.loading;

    return Scaffold(
      backgroundColor: AppColors.lightPrimaryBackgroundColor,
      appBar: const CustomAppBar(
        title: 'Settings',
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Change Password Section
              _buildChangePasswordSection(isLoading),
              const SizedBox(height: 32),

              // Newsletter Section
              _buildNewsletterSection(),
              const SizedBox(height: 32),

              // Remove Account Section
              _buildRemoveAccountSection(),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  // Build Change Password Section
  Widget _buildChangePasswordSection(bool isLoading) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Change Password:',
          size: 15,
          weight: FontWeight.w500,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 16),

        // Current Password Field
        _buildPasswordField(
          label: 'Current Password',
          controller: _currentPasswordController,
          isVisible: _isCurrentPasswordVisible,
          onVisibilityToggle: () {
            setState(() {
              _isCurrentPasswordVisible = !_isCurrentPasswordVisible;
            });
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return 'Please enter your current password';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),

        // New Password Field
        _buildPasswordField(
          label: 'New Password',
          controller: _newPasswordController,
          isVisible: _isNewPasswordVisible,
          onVisibilityToggle: () {
            setState(() {
              _isNewPasswordVisible = !_isNewPasswordVisible;
            });
          },
          validator: Validator.validatePassword,
        ),
        const SizedBox(height: 16),

        // Confirm New Password Field
        _buildPasswordField(
          label: 'Confirm New Password',
          controller: _confirmPasswordController,
          isVisible: _isConfirmPasswordVisible,
          onVisibilityToggle: () {
            setState(() {
              _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
            });
          },
          validator: _validateConfirmPassword,
        ),
        const SizedBox(height: 24),

        // Save Password Button
        CustomButton(
          text: 'Save Password',
          onPressed: isLoading ? () {} : _handleSavePassword,
          width: double.infinity,
          height: 48,
          fontSize: 16,
          fontFamily: 'Inter',
          borderRadius: 10,
          isLoading: isLoading,
        ),
      ],
    );
  }

  // Build Password Field with visibility toggle
  Widget _buildPasswordField({
    required String label,
    required TextEditingController controller,
    required bool isVisible,
    required VoidCallback onVisibilityToggle,
    required String? Function(String?) validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: label,
          size: 14,
          weight: FontWeight.w400,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 8),
        CustomInputField(
          hintText: '••••••••',
          controller: controller,
          isPassword: !isVisible,
          validator: validator,
          borderRadius: 6,
          fontSize: 14,
        ),
      ],
    );
  }

  // Build Newsletter Section
  Widget _buildNewsletterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        CustomText(
          text: 'Newsletter:',
          size: 15,
          weight: FontWeight.w500,
          color: AppColors.primaryLightTextColor,
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomText(
                text: 'Subscribe Newsletter',
                size: 14,
                weight: FontWeight.w400,
                color: AppColors.primaryLightTextColor,
              ),
            ),
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: _subscribeNewsletter
                    ? AppColors.primaryColor
                    : Colors.transparent,
                border: Border.all(
                  color: _subscribeNewsletter
                      ? AppColors.primaryColor
                      : AppColors.lightGreyColor,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(4),
              ),
              child: _subscribeNewsletter
                  ? const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    )
                  : null,
            ),
          ],
        ),
      ],
    );
  }

  // Build Remove Account Section
  Widget _buildRemoveAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 40),
        Center(
          child: GestureDetector(
            onTap: _handleRemoveAccount,
            child: CustomText(
              text: 'Remove Account',
              size: 16,
              weight: FontWeight.w500,
              color: AppColors.primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
      ],
    );
  }
}
